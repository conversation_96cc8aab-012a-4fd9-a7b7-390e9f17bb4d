import { useState } from 'react';
import { useLanguage } from '../contexts/LanguageContext';
import TechBackground from './TechBackground';
import TechEffects from './TechEffects';
import './Skills.css';

const Skills = () => {
  const { t } = useLanguage();
  const [activeCategory, setActiveCategory] = useState('frontend');

  const skillCategories = {
    frontend: {
      title: t('skills.categories.frontend'),
      icon: '🎨',
      skills: [
        { name: 'JavaScript', level: 70, description: t('skills.skillDescriptions.javascript') },
        { name: 'React', level: 65, description: t('skills.skillDescriptions.react') },
        { name: 'HTML5', level: 95, description: t('skills.skillDescriptions.html5') },
        { name: 'CSS3', level: 90, description: t('skills.skillDescriptions.css3') },
        { name: 'Vite', level: 60, description: t('skills.skillDescriptions.vite') }
      ]
    },
    backend: {
      title: t('skills.categories.backend'),
      icon: '⚙️',
      skills: [
        { name: 'Node.js', level: 92, description: t('skills.skillDescriptions.nodejs') },
        { name: 'Discord.js', level: 95, description: t('skills.skillDescriptions.discordjs') },
        { name: 'REST APIs', level: 85, description: t('skills.skillDescriptions.restapis') }
      ]
    },
    tools: {
      title: t('skills.categories.tools'),
      icon: '🛠️',
      skills: [
        { name: 'Git', level: 90, description: t('skills.skillDescriptions.git') },
        { name: 'VS Code', level: 95, description: t('skills.skillDescriptions.vscode') },
        { name: 'npm/yarn', level: 90, description: t('skills.skillDescriptions.npm') },
        { name: 'Postman', level: 85, description: t('skills.skillDescriptions.postman') },
        { name: 'Chrome DevTools', level: 88, description: t('skills.skillDescriptions.devtools') }
      ]
    }
  };

  return (
    <section id="skills" className="skills section">
      <TechBackground variant="skills" intensity="medium" />
      <TechEffects variant="skills" />
      <div className="container">
        <div className="skills__header text-center">
          <h2 className="skills__title">{t('skills.title')}</h2>
          <p className="skills__subtitle">
            {t('skills.subtitle')}
          </p>
        </div>

        <div className="skills__categories">
          {Object.entries(skillCategories).map(([key, category]) => (
            <button
              key={key}
              className={`skills__category-btn ${activeCategory === key ? 'skills__category-btn--active' : ''}`}
              onClick={() => setActiveCategory(key)}
            >
              <span className="skills__category-icon">{category.icon}</span>
              <span className="skills__category-title">{category.title}</span>
            </button>
          ))}
        </div>

        <div className="skills__content">
          <div className="skills__grid">
            {skillCategories[activeCategory].skills.map((skill, index) => (
              <div key={skill.name} className="skills__card" style={{ animationDelay: `${index * 0.1}s` }}>
                <div className="skills__card-header">
                  <h3 className="skills__skill-name">{skill.name}</h3>
                  <span className="skills__skill-percentage">{skill.level}%</span>
                </div>
                
                <div className="skills__progress-bar">
                  <div 
                    className="skills__progress-fill"
                    style={{ width: `${skill.level}%` }}
                  ></div>
                </div>
                
                <p className="skills__skill-description">{skill.description}</p>
              </div>
            ))}
          </div>
        </div>

        <div className="skills__highlight">
          <div className="skills__highlight-content">
            <h3>{t('skills.expertise.title')}</h3>
            <div className="skills__expertise-grid">
              {t('skills.expertise.items').map((item, index) => (
                <div key={index} className="skills__expertise-item">
                  <div className="skills__expertise-icon">
                    {['🤖', '⚡', '📱'][index]}
                  </div>
                  <h4>{item.title}</h4>
                  <p>{item.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Skills;
