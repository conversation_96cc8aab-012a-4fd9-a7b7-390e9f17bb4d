import { useEffect, useRef } from 'react';
import './TechBackground.css';

const TechBackground = ({ variant = 'default', intensity = 'medium' }) => {
  const canvasRef = useRef(null);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    let animationId;
    let particles = [];

    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };

    const createParticles = () => {
      particles = [];
      const particleCount = intensity === 'low' ? 30 : intensity === 'high' ? 80 : 50;
      
      for (let i = 0; i < particleCount; i++) {
        particles.push({
          x: Math.random() * canvas.width,
          y: Math.random() * canvas.height,
          size: Math.random() * 2 + 1,
          speedX: (Math.random() - 0.5) * 0.5,
          speedY: (Math.random() - 0.5) * 0.5,
          opacity: Math.random() * 0.5 + 0.2,
          type: Math.random() > 0.7 ? 'code' : 'dot'
        });
      }
    };

    const drawParticle = (particle) => {
      ctx.save();
      ctx.globalAlpha = particle.opacity;
      
      if (particle.type === 'code') {
        // Draw code-like symbols
        ctx.fillStyle = variant === 'hero' ? '#2563eb' : '#64748b';
        ctx.font = `${particle.size * 8}px monospace`;
        const symbols = ['<', '>', '{', '}', '(', ')', '[', ']', '/', '*', '+', '-', '='];
        const symbol = symbols[Math.floor(Math.random() * symbols.length)];
        ctx.fillText(symbol, particle.x, particle.y);
      } else {
        // Draw dots
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
        ctx.fillStyle = variant === 'hero' ? '#f59e0b' : '#2563eb';
        ctx.fill();
      }
      
      ctx.restore();
    };

    const connectParticles = () => {
      for (let i = 0; i < particles.length; i++) {
        for (let j = i + 1; j < particles.length; j++) {
          const dx = particles[i].x - particles[j].x;
          const dy = particles[i].y - particles[j].y;
          const distance = Math.sqrt(dx * dx + dy * dy);
          
          if (distance < 100) {
            ctx.save();
            ctx.globalAlpha = (100 - distance) / 100 * 0.1;
            ctx.strokeStyle = variant === 'hero' ? '#2563eb' : '#64748b';
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.moveTo(particles[i].x, particles[i].y);
            ctx.lineTo(particles[j].x, particles[j].y);
            ctx.stroke();
            ctx.restore();
          }
        }
      }
    };

    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      particles.forEach(particle => {
        particle.x += particle.speedX;
        particle.y += particle.speedY;
        
        // Wrap around edges
        if (particle.x < 0) particle.x = canvas.width;
        if (particle.x > canvas.width) particle.x = 0;
        if (particle.y < 0) particle.y = canvas.height;
        if (particle.y > canvas.height) particle.y = 0;
        
        drawParticle(particle);
      });
      
      connectParticles();
      animationId = requestAnimationFrame(animate);
    };

    resizeCanvas();
    createParticles();
    animate();

    const handleResize = () => {
      resizeCanvas();
      createParticles();
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, [variant, intensity]);

  return (
    <div className={`tech-background tech-background--${variant}`}>
      <canvas
        ref={canvasRef}
        className="tech-background__canvas"
      />
      
      {/* Additional CSS-based effects */}
      <div className="tech-background__overlay">
        <div className="tech-background__grid"></div>
        <div className="tech-background__circuits">
          <div className="circuit circuit--1"></div>
          <div className="circuit circuit--2"></div>
          <div className="circuit circuit--3"></div>
        </div>
        <div className="tech-background__data-stream">
          <div className="data-line data-line--1"></div>
          <div className="data-line data-line--2"></div>
          <div className="data-line data-line--3"></div>
        </div>
      </div>
    </div>
  );
};

export default TechBackground;
