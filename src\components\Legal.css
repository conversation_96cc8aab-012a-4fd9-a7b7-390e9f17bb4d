.legal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: var(--spacing-lg);
}

.legal-modal {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  max-width: 600px;
  width: 100%;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-color);
}

.legal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-xl);
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-secondary);
}

.legal-header h2 {
  color: var(--text-primary);
  margin: 0;
  font-size: 1.5rem;
}

.legal-close {
  background: none;
  border: none;
  font-size: 2rem;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all var(--transition-fast);
}

.legal-close:hover {
  background: var(--bg-primary);
  color: var(--text-primary);
}

.legal-content {
  padding: var(--spacing-xl);
  overflow-y: auto;
  max-height: calc(80vh - 80px);
}

.legal-content h3 {
  color: var(--text-primary);
  margin-top: var(--spacing-xl);
  margin-bottom: var(--spacing-md);
  font-size: 1.25rem;
}

.legal-content h3:first-child {
  margin-top: 0;
}

.legal-content p {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: var(--spacing-md);
}

.legal-content p:last-child {
  margin-bottom: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .legal-overlay {
    padding: var(--spacing-md);
  }

  .legal-modal {
    max-height: 90vh;
  }

  .legal-header {
    padding: var(--spacing-lg);
  }

  .legal-content {
    padding: var(--spacing-lg);
    max-height: calc(90vh - 70px);
  }

  .legal-header h2 {
    font-size: 1.25rem;
  }
}
