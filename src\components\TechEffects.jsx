import { useEffect, useRef } from 'react';
import './TechEffects.css';

const TechEffects = ({ variant = 'default' }) => {
  const containerRef = useRef(null);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    // Create floating tech elements
    const createTechElements = () => {
      const techSymbols = ['⚡', '🔧', '⚙️', '🔬', '💻', '🖥️', '📡', '🛰️', '🔌', '💾', '🖲️', '⌨️'];
      const codeSymbols = ['{', '}', '<', '>', '(', ')', '[', ']', ';', ':', '=', '+', '-', '*', '/', '%', '&', '|', '^', '~'];
      
      for (let i = 0; i < 15; i++) {
        const element = document.createElement('div');
        element.className = 'tech-float-element';
        
        const isCode = Math.random() > 0.6;
        element.textContent = isCode 
          ? codeSymbols[Math.floor(Math.random() * codeSymbols.length)]
          : techSymbols[Math.floor(Math.random() * techSymbols.length)];
        
        element.style.left = Math.random() * 100 + '%';
        element.style.top = Math.random() * 100 + '%';
        element.style.animationDelay = Math.random() * 10 + 's';
        element.style.animationDuration = (15 + Math.random() * 10) + 's';
        
        if (isCode) {
          element.classList.add('tech-float-element--code');
        }
        
        container.appendChild(element);
      }
    };

    // Create scanning lines
    const createScanLines = () => {
      for (let i = 0; i < 3; i++) {
        const scanLine = document.createElement('div');
        scanLine.className = 'tech-scan-line';
        scanLine.style.animationDelay = i * 2 + 's';
        container.appendChild(scanLine);
      }
    };

    // Create data streams
    const createDataStreams = () => {
      for (let i = 0; i < 5; i++) {
        const stream = document.createElement('div');
        stream.className = 'tech-data-stream';
        stream.style.left = (i * 20 + 10) + '%';
        stream.style.animationDelay = i * 1.5 + 's';
        
        // Add binary data
        const binaryData = Array.from({length: 20}, () => Math.random() > 0.5 ? '1' : '0').join('');
        stream.textContent = binaryData;
        
        container.appendChild(stream);
      }
    };

    // Create holographic grid
    const createHoloGrid = () => {
      const grid = document.createElement('div');
      grid.className = 'tech-holo-grid';
      container.appendChild(grid);
    };

    // Create glitch effects
    const createGlitchElements = () => {
      for (let i = 0; i < 8; i++) {
        const glitch = document.createElement('div');
        glitch.className = 'tech-glitch-line';
        glitch.style.top = Math.random() * 100 + '%';
        glitch.style.animationDelay = Math.random() * 5 + 's';
        container.appendChild(glitch);
      }
    };

    createTechElements();
    createScanLines();
    createDataStreams();
    createHoloGrid();
    createGlitchElements();

    return () => {
      if (container) {
        container.innerHTML = '';
      }
    };
  }, [variant]);

  return (
    <div 
      ref={containerRef} 
      className={`tech-effects tech-effects--${variant}`}
    />
  );
};

export default TechEffects;
