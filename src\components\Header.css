.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: rgba(15, 23, 42, 0.95);
  backdrop-filter: blur(10px);
  transition: all var(--transition-normal);
  border-bottom: 1px solid transparent;
}

.header--scrolled {
  background-color: rgba(15, 23, 42, 0.98);
  border-bottom-color: var(--border-color);
  box-shadow: var(--shadow-sm);
}

.header__content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md) 0;
  transition: padding var(--transition-normal);
}

.header--scrolled .header__content {
  padding: var(--spacing-sm) 0;
}

.header__logo {
  display: flex;
  align-items: center;
}

.header__logo-text {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header__nav {
  display: flex;
  align-items: center;
}

.header__nav-list {
  display: flex;
  list-style: none;
  gap: var(--spacing-xl);
  margin: 0;
  padding: 0;
}

.header__nav-link {
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: color var(--transition-fast);
  position: relative;
  padding: var(--spacing-sm) 0;
}

.header__nav-link:hover {
  color: var(--primary-color);
  text-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
  transform: translateY(-1px);
}

.header__nav-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--primary-color);
  transition: width var(--transition-fast);
}

.header__nav-link:hover::after {
  width: 100%;
}

.header__actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.header__mobile-toggle {
  display: none;
  flex-direction: column;
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--spacing-sm);
  gap: 4px;
}

.header__mobile-toggle span {
  width: 24px;
  height: 2px;
  background-color: var(--text-primary);
  transition: all var(--transition-fast);
  transform-origin: center;
}

.header__mobile-toggle:hover span {
  background-color: var(--primary-color);
}

/* Mobile Styles */
@media (max-width: 768px) {
  .header__nav {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: var(--bg-primary);
    border-top: 1px solid var(--border-color);
    box-shadow: var(--shadow-lg);
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
  }

  .header__nav--open {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }

  .header__nav-list {
    flex-direction: column;
    gap: 0;
    padding: var(--spacing-lg) 0;
  }

  .header__nav-link {
    padding: var(--spacing-md) var(--spacing-lg);
    width: 100%;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
  }

  .header__nav-link:last-child {
    border-bottom: none;
  }

  .header__nav-link::after {
    display: none;
  }

  .header__actions {
    gap: var(--spacing-md);
  }

  .header__mobile-toggle {
    display: flex;
  }

  .header__nav--open + .header__mobile-toggle span:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
  }

  .header__nav--open + .header__mobile-toggle span:nth-child(2) {
    opacity: 0;
  }

  .header__nav--open + .header__mobile-toggle span:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
  }
}
