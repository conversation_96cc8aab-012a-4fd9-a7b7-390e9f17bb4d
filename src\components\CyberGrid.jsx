import { useEffect, useRef } from 'react';
import './CyberGrid.css';

const CyberGrid = ({ variant = 'default' }) => {
  const canvasRef = useRef(null);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    let animationId;
    let time = 0;

    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };

    const drawGrid = () => {
      const gridSize = 50;
      const cols = Math.ceil(canvas.width / gridSize);
      const rows = Math.ceil(canvas.height / gridSize);

      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      // Set line style
      ctx.strokeStyle = variant === 'contact' ? 'rgba(37, 99, 235, 0.1)' : 'rgba(245, 158, 11, 0.1)';
      ctx.lineWidth = 1;

      // Draw vertical lines
      for (let i = 0; i <= cols; i++) {
        const x = i * gridSize;
        const opacity = 0.1 + 0.05 * Math.sin(time * 0.001 + i * 0.1);
        
        ctx.save();
        ctx.globalAlpha = opacity;
        ctx.beginPath();
        ctx.moveTo(x, 0);
        ctx.lineTo(x, canvas.height);
        ctx.stroke();
        ctx.restore();
      }

      // Draw horizontal lines
      for (let i = 0; i <= rows; i++) {
        const y = i * gridSize;
        const opacity = 0.1 + 0.05 * Math.sin(time * 0.001 + i * 0.1);
        
        ctx.save();
        ctx.globalAlpha = opacity;
        ctx.beginPath();
        ctx.moveTo(0, y);
        ctx.lineTo(canvas.width, y);
        ctx.stroke();
        ctx.restore();
      }

      // Draw intersection points
      ctx.fillStyle = variant === 'contact' ? 'rgba(37, 99, 235, 0.2)' : 'rgba(245, 158, 11, 0.2)';
      
      for (let i = 0; i <= cols; i++) {
        for (let j = 0; j <= rows; j++) {
          const x = i * gridSize;
          const y = j * gridSize;
          const pulse = Math.sin(time * 0.002 + (i + j) * 0.2);
          
          if (pulse > 0.7) {
            ctx.save();
            ctx.globalAlpha = (pulse - 0.7) * 3;
            ctx.beginPath();
            ctx.arc(x, y, 2, 0, Math.PI * 2);
            ctx.fill();
            ctx.restore();
          }
        }
      }

      // Draw scanning lines
      const scanY = (Math.sin(time * 0.001) * 0.5 + 0.5) * canvas.height;
      const scanX = (Math.cos(time * 0.0008) * 0.5 + 0.5) * canvas.width;
      
      // Horizontal scan line
      ctx.save();
      ctx.globalAlpha = 0.3;
      ctx.strokeStyle = variant === 'contact' ? '#2563eb' : '#f59e0b';
      ctx.lineWidth = 2;
      ctx.beginPath();
      ctx.moveTo(0, scanY);
      ctx.lineTo(canvas.width, scanY);
      ctx.stroke();
      ctx.restore();

      // Vertical scan line
      ctx.save();
      ctx.globalAlpha = 0.2;
      ctx.strokeStyle = variant === 'contact' ? '#2563eb' : '#f59e0b';
      ctx.lineWidth = 1;
      ctx.beginPath();
      ctx.moveTo(scanX, 0);
      ctx.lineTo(scanX, canvas.height);
      ctx.stroke();
      ctx.restore();
    };

    const animate = () => {
      time += 16; // ~60fps
      drawGrid();
      animationId = requestAnimationFrame(animate);
    };

    resizeCanvas();
    animate();

    const handleResize = () => {
      resizeCanvas();
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, [variant]);

  return (
    <div className={`cyber-grid cyber-grid--${variant}`}>
      <canvas ref={canvasRef} className="cyber-grid__canvas" />
      
      {/* Additional CSS effects */}
      <div className="cyber-grid__overlay">
        <div className="cyber-grid__pulse cyber-grid__pulse--1"></div>
        <div className="cyber-grid__pulse cyber-grid__pulse--2"></div>
        <div className="cyber-grid__pulse cyber-grid__pulse--3"></div>
      </div>
    </div>
  );
};

export default CyberGrid;
