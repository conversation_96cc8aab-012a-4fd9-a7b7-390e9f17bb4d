.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
  z-index: 1;
}

.hero__background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.hero__particles {
  position: absolute;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(circle at 20% 80%, rgba(37, 99, 235, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(245, 158, 11, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(37, 99, 235, 0.05) 0%, transparent 50%);
  animation: float 20s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-20px) rotate(1deg); }
  66% { transform: translateY(-10px) rotate(-1deg); }
}

.hero__content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-3xl);
  align-items: center;
  position: relative;
  z-index: 2;
  padding: var(--spacing-3xl) 0;
}

.hero__text {
  max-width: 600px;
}

.hero__title {
  font-size: 3.5rem;
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: var(--spacing-lg);
  color: var(--text-primary);
}

.hero__name {
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
  animation: nameGlow 3s ease-in-out infinite alternate;
}

@keyframes nameGlow {
  0% {
    filter: drop-shadow(0 0 10px rgba(59, 130, 246, 0.3));
  }
  100% {
    filter: drop-shadow(0 0 20px rgba(59, 130, 246, 0.6));
  }
}

.hero__subtitle {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: var(--spacing-lg);
  min-height: 2rem;
  display: flex;
  align-items: center;
}

.hero__typing-text {
  margin-right: 2px;
}

.hero__cursor {
  animation: blink 1s infinite;
  color: var(--accent-color);
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

.hero__description {
  font-size: 1.125rem;
  line-height: 1.7;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xl);
}

.hero__buttons {
  display: flex;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
}

.hero__visual {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.hero__avatar {
  position: relative;
  width: 300px;
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero__avatar-inner {
  width: 200px;
  height: 200px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 2;
  box-shadow: var(--shadow-lg);
}

.hero__avatar-text {
  font-size: 4rem;
  font-weight: 800;
  color: white;
}

.hero__avatar-ring {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 3px solid var(--primary-color);
  border-radius: 50%;
  border-top-color: transparent;
  border-right-color: transparent;
  animation: rotate 10s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.hero__tech-icons {
  position: absolute;
  width: 100%;
  height: 100%;
}

.hero__tech-icon {
  position: absolute;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: 600;
  box-shadow: var(--shadow-md);
  animation: orbit 15s linear infinite;
}

.hero__tech-icon--js {
  top: 10%;
  right: 20%;
  color: #f7df1e;
  background: #323330;
  animation-delay: 0s;
}

.hero__tech-icon--react {
  top: 60%;
  right: 10%;
  color: #61dafb;
  animation-delay: -3.75s;
}

.hero__tech-icon--node {
  bottom: 20%;
  left: 15%;
  color: #339933;
  animation-delay: -7.5s;
}

.hero__tech-icon--discord {
  top: 20%;
  left: 10%;
  color: #5865f2;
  animation-delay: -11.25s;
}

@keyframes orbit {
  from { transform: rotate(0deg) translateX(50px) rotate(0deg); }
  to { transform: rotate(360deg) translateX(50px) rotate(-360deg); }
}

.hero__scroll-indicator {
  position: absolute;
  bottom: var(--spacing-xl);
  left: 50%;
  transform: translateX(-50%);
  z-index: 2;
}

.hero__scroll-arrow {
  width: 24px;
  height: 24px;
  border-right: 2px solid var(--primary-color);
  border-bottom: 2px solid var(--primary-color);
  transform: rotate(45deg);
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: rotate(45deg) translateY(0); }
  40% { transform: rotate(45deg) translateY(-10px); }
  60% { transform: rotate(45deg) translateY(-5px); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero__content {
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
    text-align: center;
  }

  .hero__title {
    font-size: 2.5rem;
  }

  .hero__subtitle {
    font-size: 1.25rem;
    justify-content: center;
  }

  .hero__buttons {
    justify-content: center;
  }

  .hero__avatar {
    width: 250px;
    height: 250px;
  }

  .hero__avatar-inner {
    width: 150px;
    height: 150px;
  }

  .hero__avatar-text {
    font-size: 3rem;
  }

  .hero__tech-icon {
    width: 50px;
    height: 50px;
    font-size: 1.25rem;
  }
}
