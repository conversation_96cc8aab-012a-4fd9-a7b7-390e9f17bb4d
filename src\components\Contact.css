.contact {
  background-color: var(--bg-primary);
  position: relative;
  z-index: 1;
  overflow: hidden;
}

.contact__header {
  margin-bottom: var(--spacing-3xl);
}

.contact__title {
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.contact__subtitle {
  font-size: 1.25rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

.contact__content {
  max-width: 1000px;
  margin: 0 auto;
}

.contact__info-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-3xl);
}

.contact__intro-section {
  background: var(--bg-dark);
  padding: var(--spacing-2xl);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-color);
  margin-bottom: var(--spacing-2xl);
}

.contact__intro-section h3 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  font-size: 1.5rem;
}

.contact__intro-section p {
  color: var(--text-secondary);
  line-height: 1.7;
  font-size: 1.125rem;
}

.contact__disclaimer {
  margin-top: var(--spacing-xl);
  padding: var(--spacing-lg);
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  border-left: 4px solid var(--accent-color);
}

.contact__disclaimer h4 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
  font-size: 1rem;
}

.contact__disclaimer p {
  color: var(--text-secondary);
  font-size: 0.875rem;
  line-height: 1.6;
  margin: 0;
}

.contact__methods-section {
  background: var(--bg-dark);
  padding: var(--spacing-2xl);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-color);
  margin-bottom: var(--spacing-2xl);
}

.contact__methods-section h3 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-lg);
  font-size: 1.5rem;
}

.contact__methods {
  margin-bottom: var(--spacing-2xl);
}

.contact__method {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  margin-bottom: var(--spacing-md);
  transition: all var(--transition-fast);
}

.contact__method:hover {
  transform: translateX(4px);
  box-shadow: var(--shadow-sm);
}

.contact__method-icon {
  font-size: 1.5rem;
  min-width: 1.5rem;
}

.contact__method-content {
  flex: 1;
}

.contact__method-title {
  color: var(--text-primary);
  font-weight: 600;
  margin-bottom: var(--spacing-xs);
  font-size: 1rem;
}

.contact__method-value {
  color: var(--primary-color);
  font-weight: 500;
  margin-bottom: var(--spacing-xs);
  font-size: 0.875rem;
}

.contact__method-desc {
  color: var(--text-light);
  font-size: 0.875rem;
  margin: 0;
}

.contact__portfolio-info {
  background: var(--bg-dark);
  padding: var(--spacing-xl);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-color);
}

.contact__portfolio-info h4 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.contact__status {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
  font-weight: 500;
  color: var(--text-primary);
}

.contact__status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #22c55e;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7); }
  70% { box-shadow: 0 0 0 10px rgba(34, 197, 94, 0); }
  100% { box-shadow: 0 0 0 0 rgba(34, 197, 94, 0); }
}

.contact__portfolio-info p {
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin: 0;
}

/* Removed form styles - no longer needed */

/* Responsive Design */
@media (max-width: 768px) {
  .contact__info-grid {
    gap: var(--spacing-2xl);
  }

  .contact__intro-section,
  .contact__methods-section {
    padding: var(--spacing-xl);
  }

  .contact__method {
    padding: var(--spacing-md);
  }

  .contact__portfolio-info {
    padding: var(--spacing-lg);
  }
}

@media (max-width: 480px) {
  .contact__method {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-sm);
  }

  .contact__method-icon {
    font-size: 2rem;
  }

  .contact__intro-section,
  .contact__methods-section {
    padding: var(--spacing-lg);
  }
}
