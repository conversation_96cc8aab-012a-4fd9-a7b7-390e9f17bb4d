.tech-effects {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
  z-index: 0;
}

/* Floating Tech Elements */
.tech-float-element {
  position: absolute;
  font-size: 1.5rem;
  color: rgba(59, 130, 246, 0.3);
  animation: techFloat 20s infinite linear;
  user-select: none;
}

.tech-float-element--code {
  font-family: 'Courier New', monospace;
  color: rgba(245, 158, 11, 0.4);
  font-size: 1.2rem;
}

@keyframes techFloat {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) rotate(360deg);
    opacity: 0;
  }
}

/* Scanning Lines */
.tech-scan-line {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(59, 130, 246, 0.8), 
    rgba(59, 130, 246, 1), 
    rgba(59, 130, 246, 0.8), 
    transparent
  );
  animation: scanLine 8s infinite ease-in-out;
  box-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
}

@keyframes scanLine {
  0%, 100% {
    transform: translateY(0);
    opacity: 0;
  }
  50% {
    transform: translateY(100vh);
    opacity: 1;
  }
}

/* Data Streams */
.tech-data-stream {
  position: absolute;
  top: -50px;
  width: 2px;
  height: 100px;
  background: linear-gradient(to bottom, 
    transparent, 
    rgba(245, 158, 11, 0.8), 
    transparent
  );
  animation: dataStream 6s infinite linear;
  font-family: 'Courier New', monospace;
  font-size: 8px;
  color: rgba(245, 158, 11, 0.6);
  writing-mode: vertical-rl;
  text-orientation: mixed;
  overflow: hidden;
}

@keyframes dataStream {
  0% {
    transform: translateY(-100px);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(calc(100vh + 100px));
    opacity: 0;
  }
}

/* Holographic Grid */
.tech-holo-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    linear-gradient(rgba(59, 130, 246, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(59, 130, 246, 0.03) 1px, transparent 1px),
    radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.05) 2px, transparent 2px),
    radial-gradient(circle at 75% 75%, rgba(245, 158, 11, 0.03) 1px, transparent 1px);
  background-size: 50px 50px, 50px 50px, 100px 100px, 150px 150px;
  animation: holoGrid 25s ease-in-out infinite;
}

@keyframes holoGrid {
  0%, 100% {
    transform: translate(0, 0) scale(1);
    opacity: 0.5;
  }
  25% {
    transform: translate(10px, -5px) scale(1.02);
    opacity: 0.7;
  }
  50% {
    transform: translate(-5px, 10px) scale(0.98);
    opacity: 0.6;
  }
  75% {
    transform: translate(5px, 5px) scale(1.01);
    opacity: 0.8;
  }
}

/* Glitch Lines */
.tech-glitch-line {
  position: absolute;
  left: 0;
  width: 100%;
  height: 1px;
  background: rgba(255, 0, 100, 0.5);
  animation: glitchLine 3s infinite;
}

@keyframes glitchLine {
  0%, 90%, 100% {
    opacity: 0;
    transform: scaleX(0);
  }
  91%, 99% {
    opacity: 1;
    transform: scaleX(1);
  }
  92%, 98% {
    transform: scaleX(1) translateX(10px);
  }
  93%, 97% {
    transform: scaleX(1) translateX(-10px);
  }
}

/* Variant Styles */
.tech-effects--hero {
  background: radial-gradient(ellipse at center, rgba(59, 130, 246, 0.02) 0%, transparent 70%);
}

.tech-effects--hero .tech-float-element {
  color: rgba(59, 130, 246, 0.4);
}

.tech-effects--hero .tech-scan-line {
  background: linear-gradient(90deg, 
    transparent, 
    rgba(245, 158, 11, 0.8), 
    rgba(245, 158, 11, 1), 
    rgba(245, 158, 11, 0.8), 
    transparent
  );
  box-shadow: 0 0 15px rgba(245, 158, 11, 0.6);
}

.tech-effects--skills {
  background: radial-gradient(ellipse at top, rgba(59, 130, 246, 0.02) 0%, transparent 50%);
}

.tech-effects--projects {
  background: radial-gradient(ellipse at bottom, rgba(245, 158, 11, 0.02) 0%, transparent 60%);
}

.tech-effects--projects .tech-data-stream {
  background: linear-gradient(to bottom, 
    transparent, 
    rgba(59, 130, 246, 0.8), 
    transparent
  );
  color: rgba(59, 130, 246, 0.6);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .tech-float-element {
    font-size: 1.2rem;
  }
  
  .tech-float-element--code {
    font-size: 1rem;
  }
  
  .tech-data-stream {
    font-size: 6px;
  }
  
  .tech-holo-grid {
    background-size: 30px 30px, 30px 30px, 60px 60px, 90px 90px;
  }
}

@media (prefers-reduced-motion: reduce) {
  .tech-float-element,
  .tech-scan-line,
  .tech-data-stream,
  .tech-holo-grid,
  .tech-glitch-line {
    animation: none;
  }
}
