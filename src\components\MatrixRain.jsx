import { useEffect, useRef } from 'react';
import './MatrixRain.css';

const MatrixRain = ({ intensity = 'low', color = '#2563eb' }) => {
  const canvasRef = useRef(null);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    let animationId;

    // Characters for the matrix effect - mix of code symbols and binary
    const chars = '01{}[]()<>/\\|+-=*&%$#@!?~^`.,;:ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
    const charArray = chars.split('');

    let drops = [];
    const fontSize = 14;
    let columns;

    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
      columns = Math.floor(canvas.width / fontSize);
      
      // Initialize drops array
      drops = [];
      for (let i = 0; i < columns; i++) {
        drops[i] = Math.random() * canvas.height;
      }
    };

    const draw = () => {
      // Create trailing effect
      ctx.fillStyle = 'rgba(255, 255, 255, 0.05)';
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      ctx.fillStyle = color;
      ctx.font = `${fontSize}px monospace`;

      for (let i = 0; i < drops.length; i++) {
        // Random character
        const char = charArray[Math.floor(Math.random() * charArray.length)];
        
        // Draw character
        ctx.fillText(char, i * fontSize, drops[i]);

        // Move drop down
        drops[i] += fontSize;

        // Reset drop to top with some randomness
        if (drops[i] > canvas.height && Math.random() > 0.975) {
          drops[i] = 0;
        }
      }
    };

    const animate = () => {
      draw();
      
      // Adjust speed based on intensity
      const delay = intensity === 'low' ? 150 : intensity === 'high' ? 50 : 100;
      setTimeout(() => {
        animationId = requestAnimationFrame(animate);
      }, delay);
    };

    resizeCanvas();
    animate();

    const handleResize = () => {
      resizeCanvas();
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, [intensity, color]);

  return (
    <canvas
      ref={canvasRef}
      className="matrix-rain"
      style={{ opacity: intensity === 'low' ? 0.3 : intensity === 'high' ? 0.7 : 0.5 }}
    />
  );
};

export default MatrixRain;
