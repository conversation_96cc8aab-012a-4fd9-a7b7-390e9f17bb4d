import { useState } from 'react';
import { useLanguage } from '../contexts/LanguageContext';
import './Legal.css';

const Legal = ({ isOpen, onClose, type }) => {
  const { t } = useLanguage();

  if (!isOpen) return null;

  const content = {
    de: {
      imprint: {
        title: 'Impressum',
        content: `
          <h3>Angaben gemäß § 5 TMG</h3>
          <p>
            <PERSON><PERSON> Felsberg<br>
            Carl-Harz-Straße 8a<br>
            23858 <PERSON>infeld<br>
            Deutschland
          </p>
          
          <h3>Kontakt</h3>
          <p>
            E-Mail: <EMAIL>
          </p>
          
          <h3>Hosting</h3>
          <p>
            Diese Website wird gehostet bei:<br>
            ALL-INKL.COM - Neue Medien Münnich/<br>
            Hauptstraße 68 | D-02742 Friedersdorf
          </p>

          <h3>Hinweis</h3>
          <p>
            Diese Website ist ein persönliches Portfolio ohne kommerzielle Absichten.
            Es werden keine Dienstleistungen angeboten oder verkauft. Die Website dient
            ausschließlich der Darstellung persönlicher Projekte und Fähigkeiten.
          </p>
        `
      },
      privacy: {
        title: 'Datenschutzerklärung',
        content: `
          <h3>1. Datenschutz auf einen Blick</h3>
          <p>
            Diese Website ist ein persönliches Portfolio ohne kommerzielle Absichten. 
            Es werden keine personenbezogenen Daten erhoben, gespeichert oder verarbeitet.
          </p>
          
          <h3>2. Hosting</h3>
          <p>
            Diese Website wird extern gehostet. Der Hosting-Anbieter kann technisch bedingt
            Zugriffsdaten (IP-Adresse, Browsertyp, Zugriffszeit) in Logfiles speichern.
            Diese Daten werden ausschließlich für technische Zwecke verwendet und nicht
            an Dritte weitergegeben.
          </p>
          
          <h3>3. Cookies</h3>
          <p>
            Diese Website verwendet keine Cookies oder Tracking-Technologien.
          </p>
          
          <h3>4. Externe Inhalte</h3>
          <p>
            Diese Website lädt keine externen Inhalte von Drittanbietern.
          </p>
          
          <h3>5. Kontakt</h3>
          <p>
            Bei Fragen zum Datenschutz können Sie <NAME_EMAIL> erreichen.
          </p>
        `
      }
    },
    en: {
      imprint: {
        title: 'Legal Notice',
        content: `
          <h3>Information according to § 5 TMG</h3>
          <p>
            Hannes Felsberg<br>
            Carl-Harz-Straße 8a<br>
            23858 Reinfeld<br>
            Germany
          </p>
          
          <h3>Contact</h3>
          <p>
            Email: <EMAIL>
          </p>
          
          <h3>Hosting</h3>
          <p>
            This website is hosted by:<br>
            [Name of hosting provider]<br>
            [Address of hosting provider]
          </p>

          <h3>Notice</h3>
          <p>
            This website is a personal portfolio without commercial intentions.
            No services are offered or sold. The website serves exclusively
            to showcase personal projects and skills.
          </p>
        `
      },
      privacy: {
        title: 'Privacy Policy',
        content: `
          <h3>1. Privacy at a glance</h3>
          <p>
            This website is a personal portfolio without commercial intentions. 
            No personal data is collected, stored or processed.
          </p>
          
          <h3>2. Hosting</h3>
          <p>
            This website is hosted externally. The hosting provider may technically
            store access data (IP address, browser type, access time) in log files.
            This data is used exclusively for technical purposes and is not
            shared with third parties.
          </p>
          
          <h3>3. Cookies</h3>
          <p>
            This website does not use cookies or tracking technologies.
          </p>
          
          <h3>4. External content</h3>
          <p>
            This website does not load external content from third parties.
          </p>
          
          <h3>5. Contact</h3>
          <p>
            If you have questions about data protection, you can reach <NAME_EMAIL>.
          </p>
        `
      }
    }
  };

  const currentLang = t('nav.home') === 'Home' ? 'de' : 'en';
  const currentContent = content[currentLang][type];

  return (
    <div className="legal-overlay" onClick={onClose}>
      <div className="legal-modal" onClick={(e) => e.stopPropagation()}>
        <div className="legal-header">
          <h2>{currentContent.title}</h2>
          <button className="legal-close" onClick={onClose}>×</button>
        </div>
        <div className="legal-content" dangerouslySetInnerHTML={{ __html: currentContent.content }} />
      </div>
    </div>
  );
};

export default Legal;
