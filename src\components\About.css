.about {
  background-color: var(--bg-secondary);
  position: relative;
  z-index: 1;
  overflow: hidden;
}

.about__content {
  max-width: 1000px;
  margin: 0 auto;
}

.about__header {
  margin-bottom: var(--spacing-3xl);
}

.about__title {
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.about__subtitle {
  font-size: 1.25rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

.about__grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--spacing-3xl);
  margin-bottom: var(--spacing-3xl);
}

.about__text {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.about__intro h3 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.about__intro p {
  font-size: 1.125rem;
  line-height: 1.7;
  margin-bottom: var(--spacing-md);
}

.about__highlights h4 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.about__list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.about__list li {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-sm) 0;
  font-size: 1.125rem;
  color: var(--text-secondary);
}

.about__list-icon {
  font-size: 1.5rem;
  width: 2rem;
  text-align: center;
}

.about__stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-lg);
}

.about__stat-card {
  background: var(--bg-dark);
  padding: var(--spacing-xl);
  border-radius: var(--radius-lg);
  text-align: center;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  transition: transform var(--transition-fast), box-shadow var(--transition-fast);
}

.about__stat-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-md);
}

.about__stat-number {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--primary-color);
  margin-bottom: var(--spacing-sm);
  line-height: 1;
}

.about__stat-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.about__journey {
  background: var(--bg-dark);
  padding: var(--spacing-3xl);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
}

.about__journey h3 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-xl);
  text-align: center;
}

.about__timeline {
  position: relative;
  padding-left: var(--spacing-xl);
}

.about__timeline::before {
  content: '';
  position: absolute;
  left: 12px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(to bottom, var(--primary-color), var(--accent-color));
}

.about__timeline-item {
  position: relative;
  margin-bottom: var(--spacing-xl);
  padding-left: var(--spacing-xl);
}

.about__timeline-item:last-child {
  margin-bottom: 0;
}

.about__timeline-marker {
  position: absolute;
  left: -6px;
  top: 8px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--primary-color);
  border: 3px solid white;
  box-shadow: 0 0 0 3px var(--primary-color);
}

.about__timeline-content h4 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
  font-size: 1.25rem;
}

.about__timeline-content p {
  color: var(--text-secondary);
  font-size: 1rem;
  line-height: 1.6;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .about__grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
  }

  .about__stats {
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
  }

  .about__stat-card {
    padding: var(--spacing-lg);
  }

  .about__stat-number {
    font-size: 2rem;
  }

  .about__journey {
    padding: var(--spacing-2xl);
  }

  .about__timeline {
    padding-left: var(--spacing-lg);
  }

  .about__timeline-item {
    padding-left: var(--spacing-lg);
  }
}

@media (max-width: 480px) {
  .about__stats {
    grid-template-columns: 1fr;
  }
  
  .about__list li {
    font-size: 1rem;
  }
  
  .about__intro p {
    font-size: 1rem;
  }
}
