import { useState } from 'react';
import { useLanguage } from '../contexts/LanguageContext';
import CyberGrid from './CyberGrid';
import Legal from './Legal';
import './Footer.css';

const Footer = () => {
  const { t } = useLanguage();
  const currentYear = new Date().getFullYear();
  const [legalModal, setLegalModal] = useState({ isOpen: false, type: null });

  // Social links removed - keeping footer minimal and non-commercial

  const quickLinks = [
    { name: t('nav.home'), href: '#home' },
    { name: t('nav.about'), href: '#about' },
    { name: t('nav.skills'), href: '#skills' },
    { name: t('nav.projects'), href: '#projects' },
    { name: t('nav.contact'), href: '#contact' }
  ];

  const scrollToSection = (sectionId) => {
    const element = document.getElementById(sectionId.replace('#', ''));
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <footer className="footer">
      <CyberGrid variant="footer" />
      <div className="container">
        <div className="footer__content">
          <div className="footer__main">
            <div className="footer__brand">
              <div className="footer__logo">
                <span className="footer__logo-text">HF</span>
              </div>
              <h3 className="footer__name">Hannes Felsberg</h3>
              <p className="footer__tagline">
                {t('footer.tagline')}
              </p>
              <p className="footer__description">
                {t('footer.description')}
              </p>
            </div>

            <div className="footer__links">
              <div className="footer__section">
                <h4 className="footer__section-title">{t('footer.sections.navigation')}</h4>
                <ul className="footer__link-list">
                  {quickLinks.map((link, index) => (
                    <li key={index}>
                      <button
                        onClick={() => scrollToSection(link.href)}
                        className="footer__link"
                      >
                        {link.name}
                      </button>
                    </li>
                  ))}
                </ul>
              </div>

              <div className="footer__section">
                <h4 className="footer__section-title">{t('footer.sections.technologies')}</h4>
                <ul className="footer__link-list">
                  {t('footer.technologies').map((tech, index) => (
                    <li key={index}><span className="footer__service">{tech}</span></li>
                  ))}
                </ul>
              </div>

              <div className="footer__section">
                <h4 className="footer__section-title">{t('footer.sections.contact')}</h4>
                <div className="footer__contact-info">
                  <p className="footer__contact-item">
                    <span className="footer__contact-icon">📧</span>
                    <EMAIL>
                  </p>
                  <p className="footer__contact-item">
                    <span className="footer__contact-icon">💬</span>
                    itxe7
                  </p>
                  <p className="footer__contact-item">
                    <span className="footer__contact-icon">📍</span>
                    {t('footer.contactInfo.location')}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Social section removed - keeping footer minimal and non-commercial */}
        </div>

        <div className="footer__bottom">
          <div className="footer__bottom-content">
            <p className="footer__copyright">
              © {currentYear} Hannes Felsberg. {t('footer.copyright')}
            </p>
            <div className="footer__bottom-links">
              <button
                className="footer__bottom-link"
                onClick={() => setLegalModal({ isOpen: true, type: 'privacy' })}
              >
                {t('footer.legal.privacy')}
              </button>
              <button
                className="footer__bottom-link"
                onClick={() => setLegalModal({ isOpen: true, type: 'imprint' })}
              >
                {t('footer.legal.imprint')}
              </button>
            </div>
          </div>
          
          <button
            onClick={scrollToTop}
            className="footer__scroll-top"
            title={t('footer.scrollTop')}
          >
            <span className="footer__scroll-arrow">↑</span>
          </button>
        </div>
      </div>

      <Legal
        isOpen={legalModal.isOpen}
        type={legalModal.type}
        onClose={() => setLegalModal({ isOpen: false, type: null })}
      />
    </footer>
  );
};

export default Footer;
