import { createContext, useContext, useState } from 'react';

const LanguageContext = createContext();

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

export const LanguageProvider = ({ children }) => {
  const [language, setLanguage] = useState('de');

  const translations = {
    de: {
      // Navigation
      nav: {
        home: 'Home',
        about: 'Über mich',
        skills: 'Skills',
        projects: 'Projekte',
        contact: 'Info'
      },
      
      // Hero Section
      hero: {
        greeting: 'Hi, ich bin',
        name: '<PERSON><PERSON>',
        roles: [
          'JavaScript Entwickler',
          'React Spezialist',
          'Node.js Enthusiast',
          'Discord Bot Creator'
        ],
        description: '24 Jahre alt und leidenschaftlicher Entwickler mit Fokus auf moderne Web-Technologien. Ich lerne und experimentiere mit Discord Bots und Web-Anwendungen mit JavaScript, React und Node.js.',
        buttons: {
          projects: 'Meine Proje<PERSON> an<PERSON>',
          about: 'Mehr über mich'
        }
      },
      
      // About Section
      about: {
        title: 'Über mich',
        subtitle: 'Leidenschaftlicher Entwickler mit Fokus auf moderne Technologien',
        intro: {
          title: 'Hallo! Ich bin Hannes Felsberg',
          text1: 'Als 24-jähriger Entwickler brenne ich für die Entwicklung innovativer digitaler Lösungen. Meine Reise in die Programmierung begann mit der Faszination für JavaScript und hat sich zu einer tiefen Leidenschaft für moderne Web-Technologien entwickelt.',
          text2: 'Besonders interessiert bin ich an der Entwicklung von Discord Bots, die Communities dabei unterstützen können, ihre Server zu verwalten und zu verbessern. Dabei lerne ich viel über technische Umsetzung und kreative Lösungsansätze.'
        },
        highlights: {
          title: 'Was mich auszeichnet:',
          items: [
            'Schnelle Auffassungsgabe und Lernbereitschaft',
            'Kreative Problemlösungsansätze',
            'Fokus auf benutzerfreundliche Lösungen',
            'Teamorientierte Arbeitsweise'
          ]
        },
        stats: {
          age: 'Jahre alt',
          experience: 'Jahre Erfahrung',
          bots: 'Discord Bots',
          learning: 'Lernbereitschaft'
        },
        journey: {
          title: 'Meine Entwicklungsreise',
          steps: [
            {
              title: 'Erste Schritte',
              description: 'Beginn mit HTML, CSS und JavaScript - die Grundlagen des Webs'
            },
            {
              title: 'Backend-Entwicklung',
              description: 'Vertiefung in Node.js und Server-seitige Programmierung'
            },
            {
              title: 'React & Frontend',
              description: 'Spezialisierung auf moderne Frontend-Frameworks und UI/UX'
            },
            {
              title: 'Discord Bot Entwicklung',
              description: 'Fokus auf Community-Tools und Bot-Entwicklung'
            }
          ]
        }
      },
      
      // Skills Section
      skills: {
        title: 'Meine Skills',
        subtitle: 'Technologien und Tools, mit denen ich arbeite',
        categories: {
          frontend: 'Frontend Development',
          backend: 'Backend Development',
          tools: 'Tools & Technologien'
        },
        skillDescriptions: {
          javascript: 'Meine Lieblingsprogrammiersprache',
          react: 'Moderne UI-Entwicklung',
          html5: 'Semantisches Markup',
          css3: 'Responsive Design & Animationen',
          vite: 'Modernes Build-Tool',
          nodejs: 'Server-seitige JavaScript-Entwicklung',
          discordjs: 'Discord Bot Entwicklung',
          restapis: 'API-Design und -Entwicklung',
          git: 'Versionskontrolle',
          vscode: 'Lieblings-Code-Editor',
          npm: 'Package Management',
          postman: 'API-Testing',
          devtools: 'Debugging & Performance'
        },
        expertise: {
          title: 'Besondere Expertise',
          items: [
            {
              title: 'Discord Bot Development',
              description: 'Lernen und Experimentieren mit Discord Bots mit erweiterten Funktionen wie Moderation, Custom Commands und Leveling-Systemen.'
            },
            {
              title: 'Performance Optimierung',
              description: 'Interesse an schnellen, effizienten Anwendungen mit optimierter Ladezeit und reibungsloser Benutzererfahrung.'
            },
            {
              title: 'Responsive Design',
              description: 'Erfahrung mit Anwendungen, die auf allen Geräten und Bildschirmgrößen optimal funktionieren.'
            }
          ]
        }
      },
      
      // Projects Section
      projects: {
        title: 'Meine Discord Bot Projekte',
        subtitle: 'Eine Auswahl meiner entwickelten Discord Bots mit verschiedenen Funktionen',
        features: 'Features',
        technologies: 'Technologien',
        status: {
          live: 'Live',
          beta: 'Beta'
        },
        users: 'Nutzer',
        projectData: {
          moderation: {
            title: 'Discord Moderation Bot',
            description: 'Ein umfassender Moderations-Bot mit erweiterten Funktionen für Server-Management.',
            features: [
              'Automatische Moderation',
              'Warn-System',
              'Mute/Ban Management',
              'Spam-Schutz',
              'Raid-Schutz',
              'Audit Logs'
            ]
          },
          commands: {
            title: 'Custom Commands System',
            description: 'Flexibles System für benutzerdefinierte Commands mit erweiterten Funktionen.',
            features: [
              'Custom Commands',
              'Variable System',
              'Conditional Logic',
              'User Permissions',
              'Command Cooldowns',
              'Auto-Responses'
            ]
          },
          leveling: {
            title: 'Leveling & XP System',
            description: 'Gamification-System mit Leveling, Belohnungen und Leaderboards.',
            features: [
              'XP & Leveling',
              'Role Rewards',
              'Leaderboards',
              'Custom XP Rates',
              'Voice XP',
              'Daily Bonuses'
            ]
          },
          utility: {
            title: 'Multi-Purpose Utility Bot',
            description: 'Vielseitiger Bot mit Games, Utilities und Community-Features.',
            features: [
              'Mini-Games',
              'Server Statistics',
              'Weather Info',
              'Music Player',
              'Polls & Voting',
              'Welcome System'
            ]
          }
        },
        overviewTitle: 'Was meine Discord Bots bieten',
        overviewFeatures: [
          {
            title: 'Moderation Tools',
            description: 'Umfassende Moderations-Features für sichere Server'
          },
          {
            title: 'Custom Commands',
            description: 'Flexible, benutzerdefinierte Befehle und Automatisierung'
          },
          {
            title: 'Role Management',
            description: 'Intelligente Rollen-Verwaltung und Auto-Assignments'
          },
          {
            title: 'Leveling Systems',
            description: 'Gamification mit XP, Levels und Belohnungen'
          },
          {
            title: 'Logging & Alerts',
            description: 'Detaillierte Logs und Benachrichtigungen'
          },
          {
            title: 'Games & Utilities',
            description: 'Unterhaltsame Spiele und nützliche Tools'
          }
        ],
        cta: {
          title: 'Technologie & Entwicklung',
          description: 'Alle meine Discord Bots werden mit modernen Technologien entwickelt: JavaScript, Node.js, Discord.js und verschiedene Datenbanken für optimale Performance.',
          button: 'Meine Skills ansehen'
        }
      },
      
      // Contact Section
      contact: {
        title: 'Über mich',
        subtitle: 'Mehr über meine Entwicklungsreise und Projekte',
        intro: {
          title: 'Meine Entwicklungsreise',
          description: 'Hier teile ich meine Erfahrungen und Lernfortschritte in der Softwareentwicklung. Diese Website dient als persönliches Portfolio und Dokumentation meiner Projekte - ohne kommerzielle Absichten.'
        },
        networking: {
          title: 'Vernetzen & Austausch'
        },
        methods: [
          {
            title: 'E-Mail',
            value: '<EMAIL>',
            description: 'Für Fragen oder Austausch'
          },
          {
            title: 'Discord',
            value: 'itxe7',
            description: 'Community und Entwicklung'
          },
        ],
        availability: {
          title: 'Hinweis',
          status: 'Persönliches Portfolio',
          responseTime: 'Diese Website dient ausschließlich der Darstellung meiner Projekte'
        }
      },
      
      // Footer
      footer: {
        tagline: 'JavaScript Entwickler & Discord Bot Enthusiast',
        description: 'Leidenschaftlicher Entwickler mit Fokus auf moderne Web-Technologien und Discord Bot-Entwicklung.',
        sections: {
          navigation: 'Navigation',
          technologies: 'Technologien',
          contact: 'Kontakt'
        },
        technologies: [
          'JavaScript & Node.js',
          'React & Frontend',
          'Discord.js',
          'Datenbanken',
          'API Integration'
        ],
        contactInfo: {
          location: 'Deutschland'
        },
        // social: 'Folgen Sie mir', // Removed - keeping footer minimal
        copyright: 'Persönliches Portfolio.',
        legal: {
          privacy: 'Datenschutz',
          imprint: 'Impressum'
        },
        scrollTop: 'Nach oben scrollen'
      }
    },
    
    en: {
      // Navigation
      nav: {
        home: 'Home',
        about: 'About',
        skills: 'Skills',
        projects: 'Projects',
        contact: 'Info'
      },
      
      // Hero Section
      hero: {
        greeting: 'Hi, I\'m',
        name: 'Hannes Felsberg',
        roles: [
          'JavaScript Developer',
          'React Specialist',
          'Node.js Enthusiast',
          'Discord Bot Creator'
        ],
        description: '24 years old and passionate developer focused on modern web technologies. I learn and experiment with Discord bots and web applications using JavaScript, React, and Node.js.',
        buttons: {
          projects: 'View My Projects',
          about: 'Learn More About Me'
        }
      },
      
      // About Section
      about: {
        title: 'About Me',
        subtitle: 'Passionate developer focused on modern technologies',
        intro: {
          title: 'Hello! I\'m Hannes Felsberg',
          text1: 'As a 24-year-old developer, I\'m passionate about creating innovative digital solutions. My journey into programming began with a fascination for JavaScript and has evolved into a deep passion for modern web technologies.',
          text2: 'I am particularly interested in developing Discord bots that can help communities manage and improve their servers. Through this, I learn a lot about technical implementation and creative problem-solving approaches.'
        },
        highlights: {
          title: 'What sets me apart:',
          items: [
            'Quick learning ability and eagerness to learn',
            'Creative problem-solving approaches',
            'Focus on user-friendly solutions',
            'Team-oriented work style'
          ]
        },
        stats: {
          age: 'Years Old',
          experience: 'Years Experience',
          bots: 'Discord Bots',
          learning: 'Learning Mindset'
        },
        journey: {
          title: 'My Development Journey',
          steps: [
            {
              title: 'First Steps',
              description: 'Started with HTML, CSS and JavaScript - the foundations of the web'
            },
            {
              title: 'Backend Development',
              description: 'Deepened knowledge in Node.js and server-side programming'
            },
            {
              title: 'React & Frontend',
              description: 'Specialized in modern frontend frameworks and UI/UX'
            },
            {
              title: 'Discord Bot Development',
              description: 'Focus on community tools and bot development'
            }
          ]
        }
      },
      
      // Skills Section
      skills: {
        title: 'My Skills',
        subtitle: 'Technologies and tools I work with',
        categories: {
          frontend: 'Frontend Development',
          backend: 'Backend Development',
          tools: 'Tools & Technologies'
        },
        skillDescriptions: {
          javascript: 'My favorite programming language',
          react: 'Modern UI development',
          html5: 'Semantic markup',
          css3: 'Responsive design & animations',
          vite: 'Modern build tool',
          nodejs: 'Server-side JavaScript development',
          discordjs: 'Discord bot development',
          restapis: 'API design and development',
          git: 'Version control',
          vscode: 'Favorite code editor',
          npm: 'Package management',
          postman: 'API testing',
          devtools: 'Debugging & performance'
        },
        expertise: {
          title: 'Special Expertise',
          items: [
            {
              title: 'Discord Bot Development',
              description: 'Learning and experimenting with Discord bots with advanced features like moderation, custom commands, and leveling systems.'
            },
            {
              title: 'Performance Optimization',
              description: 'Interest in fast, efficient applications with optimized loading times and smooth user experience.'
            },
            {
              title: 'Responsive Design',
              description: 'Experience with applications that work optimally on all devices and screen sizes.'
            }
          ]
        }
      },
      
      // Projects Section
      projects: {
        title: 'My Discord Bot Projects',
        subtitle: 'A selection of my developed Discord bots with various features',
        features: 'Features',
        technologies: 'Technologies',
        status: {
          live: 'Live',
          beta: 'Beta'
        },
        users: 'Users',
        projectData: {
          moderation: {
            title: 'Discord Moderation Bot',
            description: 'A comprehensive moderation bot with advanced features for server management.',
            features: [
              'Automatic Moderation',
              'Warning System',
              'Mute/Ban Management',
              'Spam Protection',
              'Raid Protection',
              'Audit Logs'
            ]
          },
          commands: {
            title: 'Custom Commands System',
            description: 'Flexible system for custom commands with advanced features.',
            features: [
              'Custom Commands',
              'Variable System',
              'Conditional Logic',
              'User Permissions',
              'Command Cooldowns',
              'Auto-Responses'
            ]
          },
          leveling: {
            title: 'Leveling & XP System',
            description: 'Gamification system with leveling, rewards, and leaderboards.',
            features: [
              'XP & Leveling',
              'Role Rewards',
              'Leaderboards',
              'Custom XP Rates',
              'Voice XP',
              'Daily Bonuses'
            ]
          },
          utility: {
            title: 'Multi-Purpose Utility Bot',
            description: 'Versatile bot with games, utilities, and community features.',
            features: [
              'Mini-Games',
              'Server Statistics',
              'Weather Info',
              'Music Player',
              'Polls & Voting',
              'Welcome System'
            ]
          }
        },
        overviewTitle: 'What my Discord bots offer',
        overviewFeatures: [
          {
            title: 'Moderation Tools',
            description: 'Comprehensive moderation features for safe servers'
          },
          {
            title: 'Custom Commands',
            description: 'Flexible, custom commands and automation'
          },
          {
            title: 'Role Management',
            description: 'Intelligent role management and auto-assignments'
          },
          {
            title: 'Leveling Systems',
            description: 'Gamification with XP, levels, and rewards'
          },
          {
            title: 'Logging & Alerts',
            description: 'Detailed logs and notifications'
          },
          {
            title: 'Games & Utilities',
            description: 'Entertaining games and useful tools'
          }
        ],
        cta: {
          title: 'Technology & Development',
          description: 'All my Discord bots are developed with modern technologies: JavaScript, Node.js, Discord.js, and various databases for optimal performance.',
          button: 'View My Skills'
        }
      },
      
      // Contact Section
      contact: {
        title: 'About Me',
        subtitle: 'More about my development journey and projects',
        intro: {
          title: 'My Development Journey',
          description: 'Here I share my experiences and learning progress in software development. This website serves as a personal portfolio and documentation of my projects - without commercial intentions.'
        },
        networking: {
          title: 'Networking & Exchange'
        },
        methods: [
          {
            title: 'Email',
            value: '<EMAIL>',
            description: 'For questions or exchange'
          },
          {
            title: 'Discord',
            value: 'itxe7',
            description: 'Community and development'
          },
        ],
        availability: {
          title: 'Note',
          status: 'Personal Portfolio',
          responseTime: 'This website serves exclusively to showcase my projects'
        }
      },
      
      // Footer
      footer: {
        tagline: 'JavaScript Developer & Discord Bot Enthusiast',
        description: 'Passionate developer focused on modern web technologies and Discord bot development.',
        sections: {
          navigation: 'Navigation',
          technologies: 'Technologies',
          contact: 'Contact'
        },
        technologies: [
          'JavaScript & Node.js',
          'React & Frontend',
          'Discord.js',
          'Databases',
          'API Integration'
        ],
        contactInfo: {
          location: 'Germany'
        },
        // social: 'Follow Me', // Removed - keeping footer minimal
        copyright: 'Personal Portfolio.',
        legal: {
          privacy: 'Privacy',
          imprint: 'Imprint'
        },
        scrollTop: 'Scroll to top'
      }
    }
  };

  const t = (key) => {
    const keys = key.split('.');
    let value = translations[language];
    
    for (const k of keys) {
      value = value?.[k];
    }
    
    return value || key;
  };

  const changeLanguage = (newLanguage) => {
    setLanguage(newLanguage);
  };

  return (
    <LanguageContext.Provider value={{ language, changeLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  );
};
