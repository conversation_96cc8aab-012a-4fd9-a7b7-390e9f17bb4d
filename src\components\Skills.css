.skills {
  background-color: var(--bg-primary);
  position: relative;
  z-index: 1;
}

.skills__header {
  margin-bottom: var(--spacing-3xl);
}

.skills__title {
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.skills__subtitle {
  font-size: 1.25rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

.skills__categories {
  display: flex;
  justify-content: center;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-3xl);
  flex-wrap: wrap;
}

.skills__category-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-xl);
  background: var(--bg-dark);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-secondary);
  box-shadow: var(--shadow-sm);
}

.skills__category-btn:hover {
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.skills__category-btn--active {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: var(--bg-primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.skills__category-icon {
  font-size: 1.5rem;
}

.skills__content {
  margin-bottom: var(--spacing-3xl);
}

.skills__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-xl);
}

.skills__card {
  background: var(--bg-dark);
  padding: var(--spacing-xl);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  transition: all var(--transition-fast);
  animation: fadeInUp 0.6s ease-out forwards;
  opacity: 0;
  transform: translateY(20px);
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.skills__card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow:
    var(--shadow-lg),
    0 0 30px rgba(59, 130, 246, 0.3),
    inset 0 0 20px rgba(59, 130, 246, 0.05);
  border-color: var(--primary-color);
  background: linear-gradient(135deg, var(--bg-dark), rgba(59, 130, 246, 0.03));
}

.skills__card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.skills__skill-name {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

.skills__skill-percentage {
  font-size: 1rem;
  font-weight: 600;
  color: var(--primary-color);
  background: var(--bg-secondary);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
}

.skills__progress-bar {
  width: 100%;
  height: 8px;
  background: var(--bg-secondary);
  border-radius: var(--radius-sm);
  overflow: hidden;
  margin-bottom: var(--spacing-md);
}

.skills__progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
  border-radius: var(--radius-sm);
  transition: width 1s ease-out 0.5s;
  position: relative;
}

.skills__progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.skills__skill-description {
  color: var(--text-secondary);
  font-size: 0.875rem;
  line-height: 1.5;
  margin: 0;
}

.skills__highlight {
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  border-radius: var(--radius-xl);
  padding: var(--spacing-3xl);
  color: white;
  position: relative;
  overflow: hidden;
}

.skills__highlight::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
  background-size: 50px 50px;
  opacity: 0.3;
}

.skills__highlight-content {
  position: relative;
  z-index: 1;
}

.skills__highlight h3 {
  text-align: center;
  font-size: 2rem;
  margin-bottom: var(--spacing-xl);
  color: white;
}

.skills__expertise-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-xl);
}

.skills__expertise-item {
  text-align: center;
  padding: var(--spacing-lg);
}

.skills__expertise-icon {
  font-size: 3rem;
  margin-bottom: var(--spacing-md);
  display: block;
}

.skills__expertise-item h4 {
  font-size: 1.25rem;
  margin-bottom: var(--spacing-md);
  color: white;
}

.skills__expertise-item p {
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .skills__categories {
    gap: var(--spacing-md);
  }

  .skills__category-btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: 0.875rem;
  }

  .skills__grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .skills__card {
    padding: var(--spacing-lg);
  }

  .skills__highlight {
    padding: var(--spacing-2xl);
  }

  .skills__highlight h3 {
    font-size: 1.5rem;
  }

  .skills__expertise-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }
}

@media (max-width: 480px) {
  .skills__categories {
    flex-direction: column;
    align-items: center;
  }

  .skills__category-btn {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }
}
