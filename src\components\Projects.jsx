import { useState } from 'react';
import { useLanguage } from '../contexts/LanguageContext';
import TechBackground from './TechBackground';
import TechEffects from './TechEffects';
import './Projects.css';

const Projects = () => {
  const { t } = useLanguage();
  const [activeProject, setActiveProject] = useState(0);

  const projects = [
    {
      title: t('projects.projectData.moderation.title'),
      description: t('projects.projectData.moderation.description'),
      image: '🛡️',
      features: t('projects.projectData.moderation.features'),
      technologies: ['Discord.js', 'Node.js', 'MongoDB', 'Express.js'],
      status: t('projects.status.live'),
      users: '10,000+'
    },
    {
      title: t('projects.projectData.commands.title'),
      description: t('projects.projectData.commands.description'),
      image: '⚡',
      features: t('projects.projectData.commands.features'),
      technologies: ['Discord.js', 'Node.js', 'SQLite', 'JavaScript'],
      status: t('projects.status.live'),
      users: '5,000+'
    },
    {
      title: t('projects.projectData.leveling.title'),
      description: t('projects.projectData.leveling.description'),
      image: '🏆',
      features: t('projects.projectData.leveling.features'),
      technologies: ['Discord.js', 'Node.js', 'PostgreSQL', 'Canvas API'],
      status: t('projects.status.live'),
      users: '15,000+'
    },
    {
      title: t('projects.projectData.utility.title'),
      description: t('projects.projectData.utility.description'),
      image: '🎮',
      features: t('projects.projectData.utility.features'),
      technologies: ['Discord.js', 'Node.js', 'Redis', 'YouTube API'],
      status: t('projects.status.beta'),
      users: '2,500+'
    }
  ];

  const allFeatures = t('projects.overviewFeatures').map((feature, index) => ({
    icon: ['🛡️', '⚡', '👥', '📊', '📝', '🎮'][index],
    title: feature.title,
    description: feature.description
  }));

  return (
    <section id="projects" className="projects section">
      <TechBackground variant="projects" intensity="medium" />
      <TechEffects variant="projects" />
      <div className="container">
        <div className="projects__header text-center">
          <h2 className="projects__title">{t('projects.title')}</h2>
          <p className="projects__subtitle">
            {t('projects.subtitle')}
          </p>
        </div>

        <div className="projects__showcase">
          <div className="projects__navigation">
            {projects.map((project, index) => (
              <button
                key={index}
                className={`projects__nav-btn ${activeProject === index ? 'projects__nav-btn--active' : ''}`}
                onClick={() => setActiveProject(index)}
              >
                <span className="projects__nav-icon">{project.image}</span>
                <span className="projects__nav-title">{project.title}</span>
              </button>
            ))}
          </div>

          <div className="projects__content">
            <div className="projects__main-card">
              <div className="projects__card-header">
                <div className="projects__card-icon">{projects[activeProject].image}</div>
                <div className="projects__card-info">
                  <h3 className="projects__card-title">{projects[activeProject].title}</h3>
                  <p className="projects__card-description">{projects[activeProject].description}</p>
                  <div className="projects__card-meta">
                    <span className={`projects__status projects__status--${projects[activeProject].status.toLowerCase()}`}>
                      {projects[activeProject].status}
                    </span>
                    <span className="projects__users">{projects[activeProject].users} {t('projects.users')}</span>
                  </div>
                </div>
              </div>

              <div className="projects__card-body">
                <div className="projects__features">
                  <h4>{t('projects.features')}</h4>
                  <div className="projects__features-grid">
                    {projects[activeProject].features.map((feature, index) => (
                      <div key={index} className="projects__feature-item">
                        <span className="projects__feature-check">✓</span>
                        {feature}
                      </div>
                    ))}
                  </div>
                </div>

                <div className="projects__technologies">
                  <h4>{t('projects.technologies')}</h4>
                  <div className="projects__tech-list">
                    {projects[activeProject].technologies.map((tech, index) => (
                      <span key={index} className="projects__tech-tag">{tech}</span>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="projects__features-overview">
          <h3 className="text-center">{t('projects.overviewTitle')}</h3>
          <div className="projects__features-grid-main">
            {allFeatures.map((feature, index) => (
              <div key={index} className="projects__feature-card">
                <div className="projects__feature-icon">{feature.icon}</div>
                <h4 className="projects__feature-title">{feature.title}</h4>
                <p className="projects__feature-desc">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>

        <div className="projects__cta">
          <div className="projects__cta-content">
            <h3>{t('projects.cta.title')}</h3>
            <p>
              {t('projects.cta.description')}
            </p>
            <button
              onClick={() => {
                const element = document.getElementById('skills');
                if (element) element.scrollIntoView({ behavior: 'smooth' });
              }}
              className="btn btn-primary"
            >
              {t('projects.cta.button')}
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Projects;
