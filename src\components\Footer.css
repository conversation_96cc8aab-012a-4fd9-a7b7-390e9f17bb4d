.footer {
  background: linear-gradient(135deg, var(--bg-dark) 0%, #1e293b 100%);
  color: white;
  padding: var(--spacing-3xl) 0 0;
  position: relative;
}

.footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
}

.footer__content {
  margin-bottom: var(--spacing-2xl);
}

.footer__main {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: var(--spacing-3xl);
  margin-bottom: var(--spacing-2xl);
}

.footer__brand {
  max-width: 400px;
}

.footer__logo {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  border-radius: 50%;
  margin-bottom: var(--spacing-lg);
}

.footer__logo-text {
  font-size: 1.5rem;
  font-weight: 800;
  color: white;
}

.footer__name {
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: var(--spacing-sm);
  color: white;
}

.footer__tagline {
  color: var(--accent-color);
  font-weight: 600;
  margin-bottom: var(--spacing-md);
  font-size: 1.125rem;
}

.footer__description {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin: 0;
}

.footer__links {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-2xl);
}

.footer__section-title {
  color: white;
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: var(--spacing-lg);
  position: relative;
}

.footer__section-title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 30px;
  height: 2px;
  background: var(--accent-color);
}

.footer__link-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer__link-list li {
  margin-bottom: var(--spacing-sm);
}

.footer__link {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  transition: color var(--transition-fast);
  font-size: 0.875rem;
  padding: 0;
  text-align: left;
}

.footer__link:hover {
  color: var(--accent-color);
}

.footer__service {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.875rem;
  display: block;
}

.footer__contact-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.footer__contact-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.875rem;
  margin: 0;
}

.footer__contact-icon {
  font-size: 1rem;
  min-width: 1rem;
}

.footer__social {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
}

.footer__social-title {
  color: white;
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: var(--spacing-lg);
  text-align: center;
}

.footer__social-links {
  display: flex;
  justify-content: center;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
}

.footer__social-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: all var(--transition-fast);
  backdrop-filter: blur(10px);
}

.footer__social-link:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  transform: translateY(-2px);
}

.footer__social-icon {
  font-size: 1.25rem;
}

.footer__social-name {
  font-size: 0.875rem;
  font-weight: 500;
}

.footer__bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: var(--spacing-xl) 0;
  position: relative;
}

.footer__bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer__copyright {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.875rem;
  margin: 0;
}

.footer__bottom-links {
  display: flex;
  gap: var(--spacing-lg);
}

.footer__bottom-link {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.875rem;
  cursor: pointer;
  transition: color var(--transition-fast);
  padding: 0;
  font-family: inherit;
}

.footer__bottom-link:hover {
  color: var(--accent-color);
}

.footer__scroll-top {
  position: fixed;
  right: var(--spacing-xl);
  bottom: var(--spacing-xl);
  width: 50px;
  height: 50px;
  background: var(--primary-color);
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  box-shadow: var(--shadow-lg);
}

.footer__scroll-top:hover {
  background: var(--primary-dark);
  transform: translateY(-4px) scale(1.1);
  box-shadow: var(--shadow-lg);
}

.footer__scroll-arrow {
  font-size: 1.25rem;
  font-weight: bold;
}

/* Responsive Design */
@media (max-width: 768px) {
  .footer__main {
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
  }

  .footer__links {
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
  }

  .footer__social-links {
    gap: var(--spacing-md);
  }

  .footer__social-link {
    padding: var(--spacing-xs) var(--spacing-sm);
  }

  .footer__bottom-content {
    flex-direction: column;
    gap: var(--spacing-md);
    text-align: center;
  }

  .footer__scroll-top {
    right: var(--spacing-lg);
    bottom: var(--spacing-lg);
    width: 45px;
    height: 45px;
  }
}

@media (max-width: 480px) {
  .footer {
    padding: var(--spacing-2xl) 0 0;
  }

  .footer__links {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .footer__social-links {
    flex-direction: column;
    align-items: center;
  }

  .footer__social-link {
    width: 100%;
    max-width: 200px;
    justify-content: center;
  }

  .footer__bottom-links {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
}
