.cyber-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 0;
  pointer-events: none;
}

.cyber-grid__canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.cyber-grid__overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.cyber-grid__pulse {
  position: absolute;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(37, 99, 235, 0.1) 0%, transparent 70%);
  animation: cyberPulse 4s ease-in-out infinite;
}

.cyber-grid__pulse--1 {
  top: 20%;
  left: 10%;
  width: 200px;
  height: 200px;
  animation-delay: 0s;
}

.cyber-grid__pulse--2 {
  top: 60%;
  right: 15%;
  width: 150px;
  height: 150px;
  animation-delay: 1.5s;
}

.cyber-grid__pulse--3 {
  bottom: 20%;
  left: 30%;
  width: 180px;
  height: 180px;
  animation-delay: 3s;
}

@keyframes cyberPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.1;
  }
}

/* Contact variant */
.cyber-grid--contact .cyber-grid__pulse {
  background: radial-gradient(circle, rgba(37, 99, 235, 0.08) 0%, transparent 70%);
}

.cyber-grid--contact::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    linear-gradient(90deg, transparent 49%, rgba(37, 99, 235, 0.03) 50%, transparent 51%),
    linear-gradient(0deg, transparent 49%, rgba(37, 99, 235, 0.03) 50%, transparent 51%);
  background-size: 100px 100px;
  animation: gridShift 20s linear infinite;
}

@keyframes gridShift {
  0% { transform: translate(0, 0); }
  100% { transform: translate(100px, 100px); }
}

/* Footer variant */
.cyber-grid--footer .cyber-grid__pulse {
  background: radial-gradient(circle, rgba(245, 158, 11, 0.1) 0%, transparent 70%);
}

.cyber-grid--footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    radial-gradient(circle at 25% 25%, rgba(245, 158, 11, 0.02) 2px, transparent 2px),
    radial-gradient(circle at 75% 75%, rgba(37, 99, 235, 0.02) 1px, transparent 1px);
  background-size: 80px 80px, 120px 120px;
  animation: footerPattern 30s ease-in-out infinite;
}

@keyframes footerPattern {
  0%, 100% { transform: translate(0, 0) rotate(0deg); }
  33% { transform: translate(20px, -10px) rotate(1deg); }
  66% { transform: translate(-10px, 20px) rotate(-1deg); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .cyber-grid__pulse {
    width: 100px !important;
    height: 100px !important;
  }
  
  .cyber-grid--contact::before,
  .cyber-grid--footer::before {
    background-size: 50px 50px, 70px 70px;
  }
}

@media (prefers-reduced-motion: reduce) {
  .cyber-grid__pulse,
  .cyber-grid--contact::before,
  .cyber-grid--footer::before {
    animation: none;
  }
  
  .cyber-grid__canvas {
    display: none;
  }
}
