.neon-border {
  position: relative;
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.neon-border__content {
  position: relative;
  z-index: 2;
  width: 100%;
  height: 100%;
}

.neon-border__glow {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: var(--radius-lg);
  z-index: 1;
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.neon-border__pulse {
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border-radius: var(--radius-lg);
  z-index: 0;
  opacity: 0;
  animation: neonPulse 3s ease-in-out infinite;
}

@keyframes neonPulse {
  0%, 100% {
    opacity: 0;
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(1.02);
  }
}

/* Primary Variant */
.neon-border--primary .neon-border__glow {
  background: linear-gradient(45deg, 
    rgba(59, 130, 246, 0.5), 
    rgba(245, 158, 11, 0.5), 
    rgba(59, 130, 246, 0.5)
  );
  background-size: 200% 200%;
  animation: neonGradient 4s ease-in-out infinite;
}

.neon-border--primary .neon-border__pulse {
  background: linear-gradient(45deg, 
    rgba(59, 130, 246, 0.3), 
    rgba(245, 158, 11, 0.3), 
    rgba(59, 130, 246, 0.3)
  );
  background-size: 200% 200%;
  animation: neonGradient 4s ease-in-out infinite, neonPulse 3s ease-in-out infinite;
}

@keyframes neonGradient {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Secondary Variant */
.neon-border--secondary .neon-border__glow {
  background: linear-gradient(45deg, 
    rgba(245, 158, 11, 0.5), 
    rgba(59, 130, 246, 0.5), 
    rgba(245, 158, 11, 0.5)
  );
  background-size: 200% 200%;
  animation: neonGradient 4s ease-in-out infinite reverse;
}

.neon-border--secondary .neon-border__pulse {
  background: linear-gradient(45deg, 
    rgba(245, 158, 11, 0.3), 
    rgba(59, 130, 246, 0.3), 
    rgba(245, 158, 11, 0.3)
  );
  background-size: 200% 200%;
  animation: neonGradient 4s ease-in-out infinite reverse, neonPulse 3s ease-in-out infinite;
}

/* Success Variant */
.neon-border--success .neon-border__glow {
  background: linear-gradient(45deg, 
    rgba(34, 197, 94, 0.5), 
    rgba(59, 130, 246, 0.5), 
    rgba(34, 197, 94, 0.5)
  );
  background-size: 200% 200%;
  animation: neonGradient 4s ease-in-out infinite;
}

.neon-border--success .neon-border__pulse {
  background: linear-gradient(45deg, 
    rgba(34, 197, 94, 0.3), 
    rgba(59, 130, 246, 0.3), 
    rgba(34, 197, 94, 0.3)
  );
  background-size: 200% 200%;
  animation: neonGradient 4s ease-in-out infinite, neonPulse 3s ease-in-out infinite;
}

/* Intensity Variants */
.neon-border--low:hover .neon-border__glow {
  opacity: 0.3;
}

.neon-border--medium:hover .neon-border__glow {
  opacity: 0.6;
}

.neon-border--high:hover .neon-border__glow {
  opacity: 0.9;
}

.neon-border--always-on .neon-border__glow {
  opacity: 0.6;
}

.neon-border--always-on:hover .neon-border__glow {
  opacity: 1;
}

/* Special Effects */
.neon-border--glitch {
  animation: glitchBorder 5s infinite;
}

@keyframes glitchBorder {
  0%, 90%, 100% {
    transform: translate(0);
  }
  91% {
    transform: translate(2px, 0);
  }
  92% {
    transform: translate(-2px, 0);
  }
  93% {
    transform: translate(0, 2px);
  }
  94% {
    transform: translate(0, -2px);
  }
}

.neon-border--scan::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(59, 130, 246, 0.3), 
    transparent
  );
  animation: scanEffect 3s infinite;
  z-index: 3;
}

@keyframes scanEffect {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .neon-border__glow,
  .neon-border__pulse {
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
  }
  
  .neon-border__pulse {
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
  }
}

@media (prefers-reduced-motion: reduce) {
  .neon-border__glow,
  .neon-border__pulse {
    animation: none;
  }
  
  .neon-border--glitch {
    animation: none;
  }
  
  .neon-border--scan::before {
    animation: none;
  }
}
