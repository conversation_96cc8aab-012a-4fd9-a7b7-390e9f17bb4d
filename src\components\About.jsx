import { useLanguage } from '../contexts/LanguageContext';
import MatrixRain from './MatrixRain';
import './About.css';

const About = () => {
  const { t } = useLanguage();

  return (
    <section id="about" className="about section">
      <MatrixRain intensity="low" color="rgba(37, 99, 235, 0.1)" />
      <div className="container">
        <div className="about__content">
          <div className="about__header text-center">
            <h2 className="about__title">{t('about.title')}</h2>
            <p className="about__subtitle">
              {t('about.subtitle')}
            </p>
          </div>

          <div className="about__grid">
            <div className="about__text">
              <div className="about__intro">
                <h3>{t('about.intro.title')}</h3>
                <p>
                  {t('about.intro.text1')}
                </p>
                <p>
                  {t('about.intro.text2')}
                </p>
              </div>

              <div className="about__highlights">
                <h4>{t('about.highlights.title')}</h4>
                <ul className="about__list">
                  {t('about.highlights.items').map((item, index) => (
                    <li key={index}>
                      <span className="about__list-icon">
                        {['🚀', '💡', '🎯', '🤝'][index]}
                      </span>
                      {item}
                    </li>
                  ))}
                </ul>
              </div>
            </div>

            <div className="about__stats">
              <div className="about__stat-card">
                <div className="about__stat-number">24</div>
                <div className="about__stat-label">{t('about.stats.age')}</div>
              </div>

              <div className="about__stat-card">
                <div className="about__stat-number">3+</div>
                <div className="about__stat-label">{t('about.stats.experience')}</div>
              </div>

              <div className="about__stat-card">
                <div className="about__stat-number">50+</div>
                <div className="about__stat-label">{t('about.stats.bots')}</div>
              </div>

              <div className="about__stat-card">
                <div className="about__stat-number">∞</div>
                <div className="about__stat-label">{t('about.stats.learning')}</div>
              </div>
            </div>
          </div>

          <div className="about__journey">
            <h3>{t('about.journey.title')}</h3>
            <div className="about__timeline">
              {t('about.journey.steps').map((step, index) => (
                <div key={index} className="about__timeline-item">
                  <div className="about__timeline-marker"></div>
                  <div className="about__timeline-content">
                    <h4>{step.title}</h4>
                    <p>{step.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
