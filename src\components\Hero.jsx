import { useState, useEffect } from 'react';
import { useLanguage } from '../contexts/LanguageContext';
import TechBackground from './TechBackground';
import TechEffects from './TechEffects';
import './Hero.css';

const Hero = () => {
  const { t } = useLanguage();
  const [displayText, setDisplayText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isDeleting, setIsDeleting] = useState(false);

  const texts = t('hero.roles');

  useEffect(() => {
    const currentText = texts[currentIndex];
    const timeout = setTimeout(() => {
      if (!isDeleting) {
        if (displayText.length < currentText.length) {
          setDisplayText(currentText.slice(0, displayText.length + 1));
        } else {
          setTimeout(() => setIsDeleting(true), 2000);
        }
      } else {
        if (displayText.length > 0) {
          setDisplayText(displayText.slice(0, -1));
        } else {
          setIsDeleting(false);
          setCurrentIndex((prev) => (prev + 1) % texts.length);
        }
      }
    }, isDeleting ? 50 : 100);

    return () => clearTimeout(timeout);
  }, [displayText, currentIndex, isDeleting, texts]);

  const scrollToSection = (sectionId) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section id="home" className="hero">
      <TechBackground variant="hero" intensity="high" />
      <TechEffects variant="hero" />
      <div className="hero__background">
        <div className="hero__particles"></div>
      </div>
      
      <div className="container">
        <div className="hero__content">
          <div className="hero__text">
            <h1 className="hero__title">
              {t('hero.greeting')} <span className="hero__name">{t('hero.name')}</span>
            </h1>
            <div className="hero__subtitle">
              <span className="hero__typing-text">{displayText}</span>
              <span className="hero__cursor">|</span>
            </div>
            <p className="hero__description">
              {t('hero.description')}
            </p>
            <div className="hero__buttons">
              <button
                onClick={() => scrollToSection('projects')}
                className="btn btn-primary"
              >
                {t('hero.buttons.projects')}
              </button>
              <button
                onClick={() => scrollToSection('about')}
                className="btn btn-secondary"
              >
                {t('hero.buttons.about')}
              </button>
            </div>
          </div>
          
          <div className="hero__visual">
            <div className="hero__avatar">
              <div className="hero__avatar-inner">
                <span className="hero__avatar-text">HF</span>
              </div>
              <div className="hero__avatar-ring"></div>
            </div>
            
            <div className="hero__tech-icons">
              <div className="hero__tech-icon hero__tech-icon--js">JS</div>
              <div className="hero__tech-icon hero__tech-icon--react">⚛️</div>
              <div className="hero__tech-icon hero__tech-icon--node">📦</div>
              <div className="hero__tech-icon hero__tech-icon--discord">🤖</div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="hero__scroll-indicator">
        <div className="hero__scroll-arrow"></div>
      </div>
    </section>
  );
};

export default Hero;
