import { useLanguage } from '../contexts/LanguageContext';
import CyberGrid from './CyberGrid';
import './Contact.css';

const Contact = () => {
  const { t } = useLanguage();

  const contactMethods = t('contact.methods').map((method, index) => ({
    icon: ['📧', '💬'][index],
    ...method
  }));

  return (
    <section id="contact" className="contact section">
      <CyberGrid variant="contact" />
      <div className="container">
        <div className="contact__header text-center">
          <h2 className="contact__title">{t('contact.title')}</h2>
          <p className="contact__subtitle">
            {t('contact.subtitle')}
          </p>
        </div>

        <div className="contact__content">
          <div className="contact__info-grid">
            <div className="contact__intro-section">
              <h3>{t('contact.intro.title')}</h3>
              <p>
                {t('contact.intro.description')}
              </p>

              <div className="contact__disclaimer">
                <h4>Rechtlicher Hinweis</h4>
                <p>
                  Diese Website ist ein persönliches Portfolio ohne kommerzielle Absichten.
                  Es werden keine Dienstleistungen angeboten oder verkauft. Alle gezeigten
                  Projekte dienen ausschließlich der Dokumentation meiner Lernfortschritte
                  und Entwicklungserfahrungen.
                </p>
              </div>
            </div>

            <div className="contact__methods-section">
              <h3>{t('contact.networking.title')}</h3>
              <div className="contact__methods">
                {contactMethods.map((method, index) => (
                  <div key={index} className="contact__method">
                    <div className="contact__method-icon">{method.icon}</div>
                    <div className="contact__method-content">
                      <h4 className="contact__method-title">{method.title}</h4>
                      <p className="contact__method-value">{method.value}</p>
                      <p className="contact__method-desc">{method.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="contact__portfolio-info">
              <h4>{t('contact.availability.title')}</h4>
              <div className="contact__status">
                <div className="contact__status-indicator"></div>
                <span>{t('contact.availability.status')}</span>
              </div>
              <p>
                {t('contact.availability.responseTime')}
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Contact;
