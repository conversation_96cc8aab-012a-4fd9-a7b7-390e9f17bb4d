.language-selector {
  position: relative;
  display: inline-block;
}

.language-selector__trigger {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--bg-dark);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: 0.875rem;
  font-weight: 500;
  min-width: 120px;
  justify-content: space-between;
}

.language-selector__trigger:hover {
  border-color: var(--primary-color);
  color: var(--text-primary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.language-selector__flag {
  font-size: 1rem;
  line-height: 1;
}

.language-selector__name {
  flex: 1;
  text-align: left;
}

.language-selector__arrow {
  font-size: 0.75rem;
  transition: transform var(--transition-fast);
  color: var(--text-light);
}

.language-selector__arrow--open {
  transform: rotate(180deg);
}

.language-selector__dropdown {
  position: absolute;
  top: calc(100% + 4px);
  left: 0;
  right: 0;
  background: var(--bg-dark);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  z-index: 1000;
  overflow: hidden;
  animation: dropdownSlide 0.2s ease-out;
}

@keyframes dropdownSlide {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.language-selector__option {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: 0.875rem;
  font-weight: 500;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

.language-selector__option:last-child {
  border-bottom: none;
}

.language-selector__option:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.language-selector__option--active {
  background: var(--primary-color);
  color: var(--bg-primary);
}

.language-selector__option--active:hover {
  background: var(--primary-dark);
}

.language-selector__option-flag {
  font-size: 1rem;
  line-height: 1;
}

.language-selector__option-name {
  flex: 1;
}

.language-selector__check {
  font-size: 0.875rem;
  font-weight: 600;
  color: inherit;
}

/* Mobile adjustments */
@media (max-width: 768px) {
  .language-selector__trigger {
    min-width: 100px;
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 0.8rem;
  }

  .language-selector__name {
    display: none;
  }

  .language-selector__dropdown {
    min-width: 140px;
  }

  .language-selector__option-name {
    display: block;
  }
}

/* Focus styles for accessibility */
.language-selector__trigger:focus,
.language-selector__option:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* Dark theme specific adjustments */
.language-selector__trigger {
  backdrop-filter: blur(10px);
}

.language-selector__dropdown {
  backdrop-filter: blur(10px);
}
