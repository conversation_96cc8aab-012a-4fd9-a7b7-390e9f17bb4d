.tech-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 0;
  pointer-events: none;
}

.tech-background__canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.6;
}

.tech-background__overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/* Animated Grid Pattern */
.tech-background__grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 120%;
  height: 120%;
  background-image: 
    linear-gradient(rgba(37, 99, 235, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(37, 99, 235, 0.05) 1px, transparent 1px);
  background-size: 60px 60px;
  animation: gridSlide 30s linear infinite;
}

@keyframes gridSlide {
  0% { transform: translate(0, 0); }
  100% { transform: translate(60px, 60px); }
}

/* Circuit-like Lines */
.tech-background__circuits {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.circuit {
  position: absolute;
  background: linear-gradient(90deg, transparent, rgba(37, 99, 235, 0.2), transparent);
  height: 1px;
  animation: circuitFlow 8s ease-in-out infinite;
}

.circuit--1 {
  top: 20%;
  left: -100%;
  width: 300px;
  animation-delay: 0s;
}

.circuit--2 {
  top: 60%;
  right: -100%;
  width: 250px;
  animation-delay: 2s;
  animation-direction: reverse;
}

.circuit--3 {
  top: 80%;
  left: -100%;
  width: 400px;
  animation-delay: 4s;
}

@keyframes circuitFlow {
  0% { transform: translateX(0); opacity: 0; }
  10% { opacity: 1; }
  90% { opacity: 1; }
  100% { transform: translateX(calc(100vw + 100px)); opacity: 0; }
}

/* Data Stream Effect */
.tech-background__data-stream {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.data-line {
  position: absolute;
  width: 2px;
  background: linear-gradient(to bottom, transparent, rgba(245, 158, 11, 0.4), transparent);
  animation: dataFlow 6s linear infinite;
}

.data-line--1 {
  left: 10%;
  height: 200px;
  animation-delay: 0s;
}

.data-line--2 {
  left: 70%;
  height: 150px;
  animation-delay: 2s;
}

.data-line--3 {
  left: 40%;
  height: 180px;
  animation-delay: 4s;
}

@keyframes dataFlow {
  0% { transform: translateY(-100%); opacity: 0; }
  10% { opacity: 1; }
  90% { opacity: 1; }
  100% { transform: translateY(calc(100vh + 100px)); opacity: 0; }
}

/* Variant Styles */
.tech-background--hero {
  background: radial-gradient(ellipse at center, rgba(37, 99, 235, 0.05) 0%, transparent 70%);
}

.tech-background--hero .circuit {
  background: linear-gradient(90deg, transparent, rgba(245, 158, 11, 0.3), transparent);
}

.tech-background--hero .tech-background__grid {
  background-image: 
    linear-gradient(rgba(245, 158, 11, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(245, 158, 11, 0.03) 1px, transparent 1px);
}

.tech-background--skills {
  background: radial-gradient(ellipse at top, rgba(37, 99, 235, 0.03) 0%, transparent 50%);
}

.tech-background--projects {
  background: radial-gradient(ellipse at bottom, rgba(245, 158, 11, 0.04) 0%, transparent 60%);
}

/* Floating Code Symbols */
.tech-background::before {
  content: '< /> { } [ ] ( ) = + - * / % & | ^ ~ ! ? : ; , .';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  font-family: var(--font-mono);
  font-size: 12px;
  color: rgba(37, 99, 235, 0.1);
  white-space: pre-wrap;
  word-spacing: 50px;
  line-height: 100px;
  animation: codeFloat 40s linear infinite;
  z-index: 1;
}

@keyframes codeFloat {
  0% { transform: translateY(100vh) rotate(0deg); }
  100% { transform: translateY(-100px) rotate(360deg); }
}

/* Hexagon Pattern */
.tech-background::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(37, 99, 235, 0.02) 2px, transparent 2px),
    radial-gradient(circle at 75% 75%, rgba(245, 158, 11, 0.02) 1px, transparent 1px);
  background-size: 100px 100px, 150px 150px;
  animation: hexagonShift 25s ease-in-out infinite;
}

@keyframes hexagonShift {
  0%, 100% { transform: translate(0, 0) scale(1); }
  33% { transform: translate(20px, -10px) scale(1.05); }
  66% { transform: translate(-10px, 20px) scale(0.95); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .tech-background__grid {
    background-size: 40px 40px;
  }
  
  .circuit {
    height: 0.5px;
  }
  
  .data-line {
    width: 1px;
  }
  
  .tech-background::before {
    font-size: 10px;
    word-spacing: 30px;
    line-height: 80px;
  }
}

@media (prefers-reduced-motion: reduce) {
  .tech-background__grid,
  .circuit,
  .data-line,
  .tech-background::before,
  .tech-background::after {
    animation: none;
  }
  
  .tech-background__canvas {
    display: none;
  }
}
