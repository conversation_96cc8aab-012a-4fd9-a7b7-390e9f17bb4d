.projects {
  background-color: var(--bg-secondary);
  position: relative;
  z-index: 1;
}

.projects__header {
  margin-bottom: var(--spacing-3xl);
}

.projects__title {
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.projects__subtitle {
  font-size: 1.25rem;
  color: var(--text-secondary);
  max-width: 700px;
  margin: 0 auto;
}

.projects__showcase {
  margin-bottom: var(--spacing-3xl);
}

.projects__navigation {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-2xl);
}

.projects__nav-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  background: var(--bg-dark);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-align: left;
  color: var(--text-secondary);
}

.projects__nav-btn:hover {
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.projects__nav-btn--active {
  border-color: var(--primary-color);
  background: var(--primary-color);
  color: var(--bg-primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.projects__nav-icon {
  font-size: 2rem;
  min-width: 2rem;
}

.projects__nav-title {
  font-weight: 600;
  font-size: 1rem;
}

.projects__main-card {
  background: var(--bg-dark);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  border: 1px solid var(--border-color);
}

.projects__card-header {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-xl);
  padding: var(--spacing-2xl);
  background: linear-gradient(135deg, var(--bg-secondary), var(--bg-dark));
}

.projects__card-icon {
  font-size: 4rem;
  min-width: 4rem;
  text-align: center;
}

.projects__card-info {
  flex: 1;
}

.projects__card-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.projects__card-description {
  font-size: 1.125rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-md);
  line-height: 1.6;
}

.projects__card-meta {
  display: flex;
  gap: var(--spacing-lg);
  align-items: center;
}

.projects__status {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.projects__status--live {
  background: #dcfce7;
  color: #166534;
}

.projects__status--beta {
  background: #fef3c7;
  color: #92400e;
}

.projects__users {
  color: var(--text-secondary);
  font-weight: 600;
}

.projects__card-body {
  padding: var(--spacing-2xl);
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--spacing-2xl);
}

.projects__features h4,
.projects__technologies h4 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-lg);
  font-size: 1.25rem;
}

.projects__features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
}

.projects__feature-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  font-weight: 500;
  color: var(--text-secondary);
}

.projects__feature-check {
  color: #22c55e;
  font-weight: 700;
}

.projects__tech-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.projects__tech-tag {
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--primary-color);
  color: white;
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 500;
}

.projects__features-overview {
  margin-bottom: var(--spacing-3xl);
}

.projects__features-overview h3 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-2xl);
  font-size: 2rem;
}

.projects__features-grid-main {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-xl);
}

.projects__feature-card {
  background: var(--bg-dark);
  padding: var(--spacing-2xl);
  border-radius: var(--radius-lg);
  text-align: center;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  transition: all var(--transition-fast);
}

.projects__feature-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.projects__feature-icon {
  font-size: 3rem;
  margin-bottom: var(--spacing-lg);
}

.projects__feature-title {
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  font-size: 1.25rem;
}

.projects__feature-desc {
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0;
}

.projects__cta {
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  border-radius: var(--radius-xl);
  padding: var(--spacing-3xl);
  text-align: center;
  color: white;
}

.projects__cta-content h3 {
  font-size: 2rem;
  margin-bottom: var(--spacing-md);
  color: white;
}

.projects__cta-content p {
  font-size: 1.125rem;
  margin-bottom: var(--spacing-xl);
  color: rgba(255, 255, 255, 0.9);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.projects__cta .btn {
  background: var(--bg-primary);
  color: var(--primary-color);
  font-weight: 600;
}

.projects__cta .btn:hover {
  background: var(--bg-dark);
  color: white;
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .projects__navigation {
    grid-template-columns: 1fr;
  }

  .projects__nav-btn {
    padding: var(--spacing-md);
  }

  .projects__card-header {
    flex-direction: column;
    text-align: center;
    padding: var(--spacing-xl);
  }

  .projects__card-body {
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
    padding: var(--spacing-xl);
  }

  .projects__features-grid {
    grid-template-columns: 1fr;
  }

  .projects__features-grid-main {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .projects__feature-card {
    padding: var(--spacing-xl);
  }

  .projects__cta {
    padding: var(--spacing-2xl);
  }

  .projects__cta-content h3 {
    font-size: 1.5rem;
  }
}
